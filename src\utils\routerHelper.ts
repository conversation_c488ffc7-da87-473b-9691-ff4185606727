import type { Router, RouteLocationRaw } from 'vue-router'

/**
 * 路由跳转辅助类
 * 解决路由跳转无反应和重复跳转问题
 */
export class RouterHelper {
  private static instance: RouterHelper
  private router: Router | null = null
  private isNavigating = false
  private navigationQueue: Array<() => void> = []
  private lastNavigationTime = 0
  private readonly NAVIGATION_THROTTLE = 300 // 300ms内防止重复跳转

  private constructor() {}

  static getInstance(): RouterHelper {
    if (!RouterHelper.instance) {
      RouterHelper.instance = new RouterHelper()
    }
    return RouterHelper.instance
  }

  setRouter(router: Router) {
    this.router = router
  }

  /**
   * 安全的路由跳转
   * @param to 目标路由
   * @param options 跳转选项
   */
  async safePush(to: RouteLocationRaw, options: { 
    force?: boolean, 
    throttle?: boolean 
  } = {}): Promise<boolean> {
    if (!this.router) {
      console.error('Router未初始化')
      return false
    }

    const now = Date.now()
    
    // 节流控制
    if (options.throttle !== false && now - this.lastNavigationTime < this.NAVIGATION_THROTTLE) {
      console.warn('路由跳转过于频繁，已忽略')
      return false
    }

    // 检查是否为相同路由
    if (!options.force && this.isSameRoute(to)) {
      console.warn('目标路由与当前路由相同，已忽略')
      return false
    }

    // 如果正在导航且不是强制跳转，加入队列
    if (this.isNavigating && !options.force) {
      return new Promise((resolve) => {
        this.navigationQueue.push(() => {
          this.safePush(to, options).then(resolve)
        })
      })
    }

    try {
      this.isNavigating = true
      this.lastNavigationTime = now
      
      console.log('开始路由跳转:', to)
      await this.router.push(to)
      console.log('路由跳转成功')
      
      return true
    } catch (error: any) {
      console.error('路由跳转失败:', error)
      
      // 处理特定错误
      if (error.name === 'NavigationDuplicated') {
        console.warn('重复导航被忽略')
        return true
      }
      
      if (error.message?.includes('Navigation cancelled')) {
        console.warn('导航被取消')
        return false
      }
      
      return false
    } finally {
      this.isNavigating = false
      this.processQueue()
    }
  }

  /**
   * 安全的路由替换
   */
  async safeReplace(to: RouteLocationRaw): Promise<boolean> {
    if (!this.router) {
      console.error('Router未初始化')
      return false
    }

    try {
      this.isNavigating = true
      console.log('开始路由替换:', to)
      await this.router.replace(to)
      console.log('路由替换成功')
      return true
    } catch (error) {
      console.error('路由替换失败:', error)
      return false
    } finally {
      this.isNavigating = false
      this.processQueue()
    }
  }

  /**
   * 检查是否为相同路由
   */
  private isSameRoute(to: RouteLocationRaw): boolean {
    if (!this.router) return false
    
    const current = this.router.currentRoute.value
    
    if (typeof to === 'string') {
      return current.path === to
    }
    
    if (to.name && current.name) {
      return current.name === to.name
    }
    
    if (to.path) {
      return current.path === to.path
    }
    
    return false
  }

  /**
   * 处理导航队列
   */
  private processQueue() {
    if (this.navigationQueue.length > 0) {
      const next = this.navigationQueue.shift()
      if (next) {
        setTimeout(next, 50) // 短暂延迟避免冲突
      }
    }
  }

  /**
   * 清空导航队列
   */
  clearQueue() {
    this.navigationQueue = []
  }

  /**
   * 获取当前导航状态
   */
  getNavigationState() {
    return {
      isNavigating: this.isNavigating,
      queueLength: this.navigationQueue.length,
      lastNavigationTime: this.lastNavigationTime
    }
  }
}

// 导出单例实例
export const routerHelper = RouterHelper.getInstance()

// 便捷方法
export const safePush = (to: RouteLocationRaw, options?: { force?: boolean, throttle?: boolean }) => {
  return routerHelper.safePush(to, options)
}

export const safeReplace = (to: RouteLocationRaw) => {
  return routerHelper.safeReplace(to)
}
