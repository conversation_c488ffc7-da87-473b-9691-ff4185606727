<template>
  <div class="router-fix-test">
    <div class="test-header">
      <h1>🔧 路由跳转修复测试</h1>
      <p>测试修复后的路由跳转功能是否正常工作</p>
    </div>

    <div class="test-grid">
      <!-- 基础路由测试 -->
      <div class="test-card">
        <h3>📍 基础路由测试</h3>
        <div class="button-group">
          <button @click="testHome" class="btn primary">首页</button>
          <button @click="testAllLive" class="btn primary">全部直播</button>
          <button @click="testSchedule" class="btn primary">赛程</button>
          <button @click="testDownload" class="btn primary">下载</button>
        </div>
      </div>

      <!-- 业务路由测试 -->
      <div class="test-card">
        <h3>🎯 业务路由测试</h3>
        <div class="button-group">
          <button @click="testRoom" class="btn success">直播间</button>
          <button @click="testUserCenter" class="btn success">个人中心</button>
          <button @click="testUserProfile" class="btn success">用户资料</button>
        </div>
      </div>

      <!-- 压力测试 -->
      <div class="test-card">
        <h3>⚡ 压力测试</h3>
        <div class="button-group">
          <button @click="testRapidClick" class="btn warning">快速点击测试</button>
          <button @click="testSameRoute" class="btn warning">相同路由测试</button>
          <button @click="testConcurrent" class="btn warning">并发测试</button>
        </div>
      </div>

      <!-- 状态监控 -->
      <div class="test-card">
        <h3>📊 状态监控</h3>
        <div class="status-grid">
          <div class="status-item">
            <span class="label">导航状态:</span>
            <span :class="['value', statusClass]">{{ navigationStatus }}</span>
          </div>
          <div class="status-item">
            <span class="label">队列长度:</span>
            <span class="value">{{ queueLength }}</span>
          </div>
          <div class="status-item">
            <span class="label">成功次数:</span>
            <span class="value success">{{ successCount }}</span>
          </div>
          <div class="status-item">
            <span class="label">失败次数:</span>
            <span class="value error">{{ errorCount }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 日志区域 -->
    <div class="log-section">
      <div class="log-header">
        <h3>📝 操作日志</h3>
        <button @click="clearLogs" class="btn small">清空</button>
      </div>
      <div class="log-container" ref="logContainer">
        <div v-for="(log, index) in logs" :key="index" :class="['log-item', log.type]">
          <span class="log-time">{{ log.time }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
        <div v-if="logs.length === 0" class="log-empty">
          暂无日志记录
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed, nextTick } from 'vue'
import { useRouter } from '@/composables/useRouter'
import { routerHelper } from '@/utils/routerHelper'

// 使用增强的路由功能
const { 
  goToHome, 
  goToAllLive, 
  goToSchedule, 
  goToDownload,
  goToRoom,
  goToUserCenter,
  push
} = useRouter()

// 状态管理
const navigationState = ref(routerHelper.getNavigationState())
const logs = ref<Array<{time: string, message: string, type: string}>>([])
const logContainer = ref<HTMLElement>()
const successCount = ref(0)
const errorCount = ref(0)

// 计算属性
const navigationStatus = computed(() => 
  navigationState.value.isNavigating ? '跳转中' : '空闲'
)
const statusClass = computed(() => 
  navigationState.value.isNavigating ? 'navigating' : 'idle'
)
const queueLength = computed(() => navigationState.value.queueLength)

// 定时更新状态
let statusInterval: number | null = null

onMounted(() => {
  statusInterval = setInterval(() => {
    navigationState.value = routerHelper.getNavigationState()
  }, 100)
  
  addLog('🚀 路由测试组件已加载', 'info')
})

onUnmounted(() => {
  if (statusInterval) {
    clearInterval(statusInterval)
  }
})

// 日志管理
const addLog = async (message: string, type: 'info' | 'success' | 'error' = 'info') => {
  logs.value.unshift({
    time: new Date().toLocaleTimeString(),
    message,
    type
  })
  
  // 限制日志数量
  if (logs.value.length > 50) {
    logs.value = logs.value.slice(0, 50)
  }
  
  // 自动滚动到顶部
  await nextTick()
  if (logContainer.value) {
    logContainer.value.scrollTop = 0
  }
}

const clearLogs = () => {
  logs.value = []
  successCount.value = 0
  errorCount.value = 0
  addLog('📝 日志已清空', 'info')
}

// 执行测试并记录结果
const executeTest = async (testName: string, testFn: () => Promise<boolean>) => {
  addLog(`🔄 开始测试: ${testName}`, 'info')
  try {
    const success = await testFn()
    if (success) {
      successCount.value++
      addLog(`✅ ${testName} - 成功`, 'success')
    } else {
      errorCount.value++
      addLog(`❌ ${testName} - 失败`, 'error')
    }
  } catch (error) {
    errorCount.value++
    addLog(`💥 ${testName} - 异常: ${error}`, 'error')
  }
}

// 测试方法
const testHome = () => executeTest('跳转首页', goToHome)
const testAllLive = () => executeTest('跳转全部直播', () => goToAllLive('all'))
const testSchedule = () => executeTest('跳转赛程', goToSchedule)
const testDownload = () => executeTest('跳转下载页面', goToDownload)
const testRoom = () => executeTest('跳转直播间', () => goToRoom(12345, 67890, 'FOOTBALL'))
const testUserCenter = () => executeTest('跳转个人中心', () => goToUserCenter())
const testUserProfile = () => executeTest('跳转用户资料', () => goToUserCenter('profile'))

const testRapidClick = async () => {
  addLog('⚡ 开始快速点击测试 (5次)', 'info')
  for (let i = 0; i < 5; i++) {
    setTimeout(() => {
      executeTest(`快速点击 ${i + 1}`, () => push('/home', { throttle: true }))
    }, i * 50)
  }
}

const testSameRoute = () => {
  const currentPath = window.location.pathname
  executeTest('相同路由跳转', () => push(currentPath))
}

const testConcurrent = async () => {
  addLog('🔀 开始并发测试 (3个同时)', 'info')
  const promises = [
    executeTest('并发测试1', () => push('/home')),
    executeTest('并发测试2', () => push('/all')),
    executeTest('并发测试3', () => push('/schedule'))
  ]
  await Promise.all(promises)
}
</script>

<style scoped lang="scss">
.router-fix-test {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.test-header {
  text-align: center;
  margin-bottom: 30px;
  
  h1 {
    color: #2c3e50;
    margin-bottom: 10px;
  }
  
  p {
    color: #7f8c8d;
    font-size: 16px;
  }
}

.test-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.test-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  border: 1px solid #e1e8ed;
  
  h3 {
    margin-bottom: 15px;
    color: #2c3e50;
    font-size: 18px;
  }
}

.button-group {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s;
  
  &.primary {
    background: #3498db;
    color: white;
    &:hover { background: #2980b9; }
  }
  
  &.success {
    background: #27ae60;
    color: white;
    &:hover { background: #229954; }
  }
  
  &.warning {
    background: #f39c12;
    color: white;
    &:hover { background: #e67e22; }
  }
  
  &.small {
    padding: 4px 12px;
    font-size: 12px;
  }
}

.status-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  background: #f8f9fa;
  border-radius: 4px;
  
  .label {
    font-size: 12px;
    color: #6c757d;
  }
  
  .value {
    font-weight: 600;
    font-size: 14px;
    
    &.navigating { color: #e74c3c; }
    &.idle { color: #27ae60; }
    &.success { color: #27ae60; }
    &.error { color: #e74c3c; }
  }
}

.log-section {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  border: 1px solid #e1e8ed;
}

.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  
  h3 {
    margin: 0;
    color: #2c3e50;
  }
}

.log-container {
  max-height: 300px;
  overflow-y: auto;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 10px;
}

.log-item {
  display: flex;
  margin-bottom: 4px;
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 12px;
  line-height: 1.4;
  
  &.info { color: #6c757d; }
  &.success { color: #27ae60; }
  &.error { color: #e74c3c; }
}

.log-time {
  margin-right: 10px;
  color: #adb5bd;
  min-width: 80px;
  flex-shrink: 0;
}

.log-empty {
  text-align: center;
  color: #adb5bd;
  font-style: italic;
  padding: 20px;
}
</style>
