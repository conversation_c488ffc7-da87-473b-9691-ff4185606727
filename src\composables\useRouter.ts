import { useRouter as useVueRouter } from 'vue-router'
import type { RouteLocationRaw } from 'vue-router'
import { safePush, safeReplace } from '@/utils/routerHelper'

/**
 * 增强的路由组合式函数
 * 提供更安全和可靠的路由跳转方法
 */
export function useRouter() {
  const router = useVueRouter()

  /**
   * 安全的路由跳转
   * @param to 目标路由
   * @param options 跳转选项
   */
  const push = async (to: RouteLocationRaw, options?: { 
    force?: boolean, 
    throttle?: boolean 
  }) => {
    return await safePush(to, options)
  }

  /**
   * 安全的路由替换
   * @param to 目标路由
   */
  const replace = async (to: RouteLocationRaw) => {
    return await safeReplace(to)
  }

  /**
   * 返回上一页
   */
  const back = () => {
    if (window.history.length > 1) {
      router.back()
    } else {
      // 如果没有历史记录，跳转到首页
      push('/')
    }
  }

  /**
   * 前进到下一页
   */
  const forward = () => {
    router.forward()
  }

  /**
   * 跳转到指定页面，支持查询参数
   */
  const go = (delta: number) => {
    router.go(delta)
  }

  /**
   * 跳转到直播间
   */
  const goToRoom = (matchId: number, userId: number, liveTypeEnum: string) => {
    return push({
      name: 'room',
      query: {
        matchId: matchId.toString(),
        userId: userId.toString(),
        liveTypeEnum
      }
    })
  }

  /**
   * 跳转到用户中心
   */
  const goToUserCenter = (subPage?: string) => {
    const path = subPage ? `/user/${subPage}` : '/user'
    return push(path)
  }

  /**
   * 跳转到全部直播页面
   */
  const goToAllLive = (type?: string) => {
    const query = type ? { type } : {}
    return push({
      path: '/all',
      query
    })
  }

  /**
   * 跳转到首页
   */
  const goToHome = () => {
    return push('/')
  }

  /**
   * 跳转到赛程页面
   */
  const goToSchedule = () => {
    return push('/schedule')
  }

  /**
   * 跳转到下载页面
   */
  const goToDownload = () => {
    return push('/download')
  }

  return {
    // 原生路由对象
    router,
    
    // 增强的路由方法
    push,
    replace,
    back,
    forward,
    go,
    
    // 业务相关的路由方法
    goToRoom,
    goToUserCenter,
    goToAllLive,
    goToHome,
    goToSchedule,
    goToDownload
  }
}

// 导出类型
export type EnhancedRouter = ReturnType<typeof useRouter>
