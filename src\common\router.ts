import HomeView from '../views/HomeView.vue'

// 创建带错误处理的异步组件加载函数
const createAsyncComponent = (importFn: () => Promise<any>, componentName: string) => {
  return () => importFn().catch(error => {
    console.error(`加载组件 ${componentName} 失败:`, error)
    // 返回一个错误组件
    return import('../components/view/404/NotFoundView.vue')
  })
}

const NotFound = createAsyncComponent(
  () => import('../components/view/404/NotFoundView.vue'),
  'NotFound'
)
const HomePageView = createAsyncComponent(
  () => import('../components/view/home/<USER>'),
  'HomePageView'
)
const AllView = createAsyncComponent(
  () => import('../components/view/all/AllView.vue'),
  'AllView'
)
const UserCenterView = createAsyncComponent(
  () => import('../components/view/user/UserCenterView.vue'),
  'UserCenterView'
)
const UserDashboard = createAsyncComponent(
  () => import('../components/view/user/UserDashboard.vue'),
  'UserDashboard'
)
const UserProfile = createAsyncComponent(
  () => import('../components/view/user/UserProfile.vue'),
  'UserProfile'
)
const UserMessages = createAsyncComponent(
  () => import('../components/view/user/UserMessages.vue'),
  'UserMessages'
)
const UserFollows = createAsyncComponent(
  () => import('../components/view/user/UserFollows.vue'),
  'UserFollows'
)
const UserAppointments = createAsyncComponent(
  () => import('../components/view/user/UserAppointments.vue'),
  'UserAppointments'
)
const UserFeedback = createAsyncComponent(
  () => import('../components/view/user/UserFeedback.vue'),
  'UserFeedback'
)
const LiveAppointments = createAsyncComponent(
  () => import('../components/view/user/LiveAppointments.vue'),
  'LiveAppointments'
)
const LiveSettings = createAsyncComponent(
  () => import('../components/view/user/LiveSettings.vue'),
  'LiveSettings'
)
const ModifyPassword = createAsyncComponent(
  () => import('../components/view/user/ModifyPassword.vue'),
  'ModifyPassword'
)
const ModifyPhone = createAsyncComponent(
  () => import('../components/view/user/ModifyPhone.vue'),
  'ModifyPhone'
)
const ScheduleView = createAsyncComponent(
  () => import('../components/view/schedule/scheduleView.vue'),
  'ScheduleView'
)
const RoomView = createAsyncComponent(
  () => import('../components/view/detail/DetailView.vue'),
  'RoomView'
)
const GlobalModifyPassword = createAsyncComponent(
  () => import('../components/view/user/GlobalModifyPassword.vue'),
  'GlobalModifyPassword'
)
const DownLoadView = createAsyncComponent(
  () => import('../components/view/download/DownLoadView.vue'),
  'DownLoadView'
)

const routes = [
  {
    path: '/',
    name: 'home',
    component: HomeView,
    redirect: '/home',
    children: [
      {
        name: 'homepage',
        path: '/home',
        component: HomePageView,
        meta: {
          title: '首页'
        }
      },
      {
        name: 'allpage',
        path: '/all',
        component: AllView,
        meta: {
          title: '全部直播'
        }
      },
      {
        name: 'schedule',
        path: '/schedule',
        component: ScheduleView,
        meta: {
          title: '赛程'
        }
      },
      {
        name: 'room',
        path: '/room',
        component: RoomView,
        meta: {
          title: '直播间'
        }
      },
      {
        name: 'global-modify-password',
        path: '/global-modify-password',
        component: GlobalModifyPassword,
        meta: {
          title: '修改密码'
        }
      },
      {
        name: 'download',
        path: '/download',
        component: DownLoadView,
        meta: {
          title: '下载APP'
        }
      },
      {
        name: 'usercenter',
        path: '/user',
        component: UserCenterView,
        redirect: '/user/dashboard',
        meta: {
          title: '个人中心'
        },
        children: [
          {
            name: 'user-dashboard',
            path: 'dashboard',
            component: UserDashboard,
            meta: {
              title: '我的首页'
            }
          },
          {
            name: 'user-profile',
            path: 'profile',
            component: UserProfile,
            meta: {
              title: '我的资料'
            }
          },
          {
            name: 'user-messages',
            path: 'messages',
            component: UserMessages,
            meta: {
              title: '我的私信'
            }
          },
          {
            name: 'user-follows',
            path: 'follows',
            component: UserFollows,
            meta: {
              title: '主播关注'
            }
          },
          {
            name: 'user-appointments',
            path: 'appointments',
            component: UserAppointments,
            meta: {
              title: '比赛关注'
            }
          },
          {
            name: 'user-feedback',
            path: 'feedback',
            component: UserFeedback,
            meta: {
              title: '我的反馈'
            }
          },
          {
            name: 'live-appointments',
            path: 'live-appointments',
            component: LiveAppointments,
            meta: {
              title: '预约直播'
            }
          },
          {
            name: 'live-settings',
            path: 'live-settings',
            component: LiveSettings,
            meta: {
              title: '直播设置'
            }
          },
          {
            name: 'modify-password',
            path: 'modify-password',
            component: ModifyPassword,
            meta: {
              title: '修改密码'
            }
          },
          {
            name: 'modify-phone',
            path: 'modify-phone',
            component: ModifyPhone,
            meta: {
              title: '修改手机号'
            }
          }
        ]
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: NotFound,
    meta: {
      title: '404错误啦'
    }
  }
]
export default routes
