<template>
  <div class="user-follows">
    <div class="page-header">
      <div class="line"></div>
      <h3>主播关注</h3>
    </div>
    <div class="page-content">
      <div v-if="collectAnchorList?.length > 0" class="follows-list" v-for="(list, index) in collectAnchorList"
        :key="list.userId">
        <div class="chat-box">
          <div class="chat-info">
            <img class="logo" :src="list.userImage" />
            <div class="info">
              <p class="name">{{ list.userName }}</p>
              <p class="desc">{{ list?.summary }}</p>
            </div>
          </div>
          <div class="unfllow-btn" @click="handleCancelFollow(list.liveId)">取消关注</div>
        </div>
        <div class="live-list">
          <div class="live-list-header">
            <div class="header-placeholder"></div>
            <div class="toggle-expand" v-if="list.roomDetailResList?.length > 3" @click="handleToggleExpand(index)">
              {{ expandedStates[index] ? '收起' : '展开更多' }}
              <img :src="expandedStates[index] ? upIcon : downIcon" class="toggle-icon"
                :class="{ 'expand': expandedStates[index] }" />
            </div>
          </div>
          <div class="live-item-container" :class="{ 'expanded': expandedStates[index] }">
            <!-- 暂无直播 -->
            <div v-if="!hasLiveBroadcast(list.roomDetailResList)" class="live-item">
              <p class="item-title">暂无直播</p>
              <div class="none-box">
                <img class="none-img" src="@/static/user/none-live-icon.png" />
              </div>
            </div>
            <!-- 正在直播列表 -->
            <div class="live-item" v-if="hasLiveBroadcast(list.roomDetailResList)"
              v-for="item in getLiveBroadcasts(list.roomDetailResList)" :key="'live-' + item.userId"
              @click="handleToRoom(item.matchId, item.userId, item.liveType)">
              <p class="item-title">
                {{ item.liveTitle }}
              </p>
              <div class="live-content-item">
                <!-- 直播封面 -->
                <img class="cover-image" :src="item.liveCover" :alt="item.liveTitle">
                <!-- Live 标识 -->
                <div class="live-badge">
                  <img class="live-dot" src="@/static/live/live-icon.gif" />Live
                </div>
                <!-- 播放按钮 -->
                <div class="play-button">
                  <img src="@/static/live/play-icon.png" />
                </div>
              </div>
            </div>
            <!-- 未直播列表 -->
            <div class="live-item" v-if="list.roomDetailResList?.length > 0"
              v-for="(item, itemIndex) in getNonLiveBroadcasts(list.roomDetailResList)" :key="item.userId"
              v-show="expandedStates[index] || itemIndex < 2">
              <p class="item-title">{{ formatDate(item.matchTime) }}</p>
              <div class="content-item">
                <div class="top">
                  <div class="classs">
                    <p class="name">{{ item.sclassName }}</p>
                  </div>
                  <span class="date">{{ dayjs(item.matchTime).format('HH:mm') }}</span>
                </div>
                <div class="bottom">
                  <div class="left" v-if="item.liveType === 'basket'">
                    <div class="team">
                      <img :src="item.awayLogo" :alt="item.awayName" />
                      <p class="name">{{ item.awayName }}</p>
                    </div>
                    <div class="team">
                      <img :src="item.homeLogo" :alt="item.homeName" />
                      <p class="name">{{ item.homeName }}</p>
                    </div>
                  </div>
                  <div class="left" v-else>
                    <div class="team">
                      <img :src="item.homeLogo" :alt="item.homeName" />
                      <p class="name">{{ item.homeName }}</p>
                    </div>
                    <div class="team">
                      <img :src="item.awayLogo" :alt="item.awayName" />
                      <p class="name">{{ item.awayName }}</p>
                    </div>
                  </div>
                  <div class="right">
                    <img v-if="!item?.collectId" src="@/static/schedule/live-icon.png" class="collect-icon"
                      @click="handleReserve(0, item.matchId, item.liveType)">
                    <img v-else src="@/static/schedule/live-icon-active.png" class="collect-icon"
                      @click="handleReserve(1, item.matchId, item.liveType)">
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div v-else class="dafault-box">
        <img src="@/static/user/msg-default-icon.png" />
        <p class="tip">暂无关注</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useMessage } from 'naive-ui'
import { formatDate } from '@/utils/formatDate'
import { cancelCollectMatchApi, collectMatchApi } from '@/api/schedule'
import { cancelFollowUserApi, getCollectAnchorPageApi } from '@/api/user'
import dayjs from 'dayjs'
import { useRouter } from 'vue-router'
import downIcon from '@/static/index/arrow-right-icon.png'
import upIcon from '@/static/index/arrow-right-icon.png'

const router = useRouter()
const message = useMessage()

const collectAnchorList = ref()
const expandedStates = ref<boolean[]>([])

const hasLiveBroadcast = (roomDetailResList: any[]) => {
  return roomDetailResList?.some((item: { liveState: number }) => item.liveState === 1)
}

const getLiveBroadcasts = (roomDetailResList: any[]) => {
  return roomDetailResList?.filter((item: { liveState: number }) => item.liveState === 1) || []
}

const getNonLiveBroadcasts = (roomDetailResList: any[]) => {
  return roomDetailResList?.filter((item: { liveState: number }) => item.liveState !== 1) || []
}

// 获取关注主播列表
const getCollectAnchorList = async (expand: boolean = true) => {
  const { data } = await getCollectAnchorPageApi({})
  collectAnchorList.value = data.records
  // 初始化展开状态数组
  if (expand) {
    expandedStates.value = new Array(data.records.length).fill(false)
  }
}

// 切换展开状态
const handleToggleExpand = (index: number) => {
  expandedStates.value[index] = !expandedStates.value[index]
}

// 取消关注
const handleCancelFollow = async (liveId: number) => {
  await cancelFollowUserApi({ liveId })
  message.success('取消关注成功')
  await getCollectAnchorList()
}

// 预约、取消预约比赛
const handleReserve = async (type: number, matchId: number, liveType: string) => {
  let data = {
    matchId,
    liveType
  }
  type ? await cancelCollectMatchApi(data) : await collectMatchApi(data)
  // 显示消息前先清除所有
  message.destroyAll()
  type ? message.success('取消预约成功') : message.success('预约成功')
  await getCollectAnchorList(false)
}

// 跳转直播间
const handleToRoom = (matchId: number, userId: number, liveTypeEnum: string) => {
  router.push(`/room?matchId=${matchId}&userId=${userId}&liveTypeEnum=${liveTypeEnum}`)
}

onMounted(() => {
  getCollectAnchorList()
})
</script>

<style lang="scss" scoped>
.user-follows {
  background-color: #fff;
  min-height: 100%;

  .page-header {
    padding: 32px 0 40px 36px;
    display: flex;
    align-items: center;

    .line {
      width: 3px;
      height: 16px;
      margin-right: 8px;
      background: #FB2B1F;
    }

    h3 {
      font-size: 18px;
      font-weight: 600;
      color: #333;
      margin: 0;
    }
  }

  .page-content {
    padding: 0 40px;

    .dafault-box {
      display: flex;
      height: 600px;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      img {
        display: block;
        width: 280px;
        height: 280px;
      }

      .tip {
        color: #818181;
        font-size: 24px;
      }
    }

    .follows-list {

      .chat-box {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .chat-info {
          display: flex;
          align-items: center;

          .logo {
            width: 50px;
            height: 50px;
            display: block;
            border-radius: 50%;
          }

          .info {
            margin-left: 10px;

            .name {
              font-size: 16px;
              font-weight: 600;
              color: #333;
            }

            .desc {
              font-size: 14px;
              color: #666;
            }
          }
        }

        .unfllow-btn {
          width: 100px;
          height: 30px;
          background: #F4F4F4;
          border-radius: 4px 4px 4px 4px;
          font-size: 14px;
          color: #818181;
          display: flex;
          justify-content: center;
          align-items: center;
          cursor: pointer;
        }
      }

      .live-list {
        margin-top: 10px;
        padding-left: 60px;
        margin-bottom: 32px;

        .live-list-header {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .header-placeholder {
            flex: 1;
          }

          .toggle-expand {
            color: #FB2B1F;
            font-size: 14px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 4px;
            padding: 4px 8px;
            border-radius: 4px;
            transition: background-color .2s;

            .toggle-icon {
              width: 16px;
              height: 16px;
            }

            .expand {
              transform: rotate(90deg);
              margin-left: 2px;
            }
          }
        }

        .live-item-container {
          display: flex;
          flex-wrap: wrap;
        }

        .expanded {
          flex-wrap: wrap;
          overflow-y: auto;
          height: calc(100vh - 450px);
        }

        .live-item {
          width: 206px;
          margin-right: 20px;
          margin-bottom: 10px;

          .item-title {
            font-weight: 600;
            font-size: 16px;
            color: #333333;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .none-box {
            background-color: #F4F4F4;
            width: 206px;
            height: 106px;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 8px;

            .none-img {
              width: 60px;
              height: 60px;
              display: block;
            }
          }

          .live-content-item {
            cursor: pointer;
            position: relative;
            width: 206px;
            height: 106px;
            margin-top: 8px;

            .cover-image {
              width: 100%;
              height: 100%;
              border-radius: 8px;
              object-fit: cover;
              transition: transform 0.2s ease;
            }

            .live-badge {
              position: absolute;
              left: 10px;
              bottom: 8px;
              background: rgba(251, 43, 31, 0.9);
              color: white;
              padding: 2px 6px;
              border-radius: 4px;
              font-size: 12px;
              font-weight: 500;
              display: flex;
              align-items: center;
              gap: 4px;

              .live-dot {
                width: 16px;
                height: 10px;
                display: block;
              }
            }

            .play-button {
              position: absolute;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%) scale(2);
              width: 48px;
              height: 48px;
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              opacity: 0;
              transition: all 0.2s ease;
            }



            &:hover .play-button {
              opacity: 1;
              transform: translate(-50%, -50%) scale(1);
            }
          }

          .content-item {
            border-radius: 8px 8px 8px 8px;
            border: 1px solid #D9D9D9;
            margin-top: 8px;
            padding: 8px;

            .top {
              display: flex;
              justify-content: space-between;
              font-size: 12px;
              color: #818181;

              .classs {
                display: flex;
                align-items: center;

                .icon {
                  display: flex;
                  width: 20px;
                  height: 20px;
                  margin-right: 2px;
                  border-radius: 50%;
                }

                .name {
                  width: 90px;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                }
              }
            }

            .bottom {
              display: flex;
              justify-content: space-between;
              align-items: center;

              .left {
                display: flex;
                flex-direction: column;

                .team {
                  display: flex;
                  align-items: center;
                  font-size: 14px;
                  color: #333333;
                  margin-top: 10px;


                  img {
                    display: block;
                    width: 24px;
                    height: 24px;
                    margin-right: 10px;
                    border-radius: 50%;
                  }

                  .name {
                    width: 100px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                  }
                }
              }

              .right {
                .collect-icon {
                  width: 20px;
                  height: 20px;
                  margin-right: 4px;
                  color: #333;
                  cursor: pointer;
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>