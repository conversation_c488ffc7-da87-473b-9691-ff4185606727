<template>
  <div class="user-appointments">
    <div class="page-header">
      <div class="line"></div>
      <h3>比赛关注</h3>
    </div>
    <div class="page-content" @scroll="handleScroll">
      <!-- 动态日期分组 -->
      <template v-if="groupedAppointments.length > 0">
        <div v-for="group in groupedAppointments" :key="group.date" class="date-group">
          <div class="date-header">
            <span class="date">{{ group.displayDate }}</span>
            <span class="today" v-if="group.isToday">今天</span>
          </div>
          <!-- 预约列表 -->
          <div class="appointment-list">
            <div class="appointment-item" v-for="item in group.items" :key="item.id">
              <div class="time-info">
                <div class="league">{{ item.sclassName }}</div>
                <div class="time">{{ dayjs(item.matchTime).format('HH:mm') }}</div>
              </div>
              <div class="match-info" v-if="item.liveType === 'basket'">
                <div class="team">
                  <img :src="item.awayLogo" :alt="item.awayName" class="team-logo">
                  <span class="team-name">{{ item.awayName }}</span>
                </div>
                <div class="team">
                  <img :src="item.homeLogo" :alt="item.homeName" class="team-logo">
                  <span class="team-name">{{ item.homeName }}</span>
                </div>
              </div>
              <div class="match-info" v-else>
                <div class="team">
                  <img :src="item.awayLogo" :alt="item.awayName" class="team-logo">
                  <span class="team-name">{{ item.awayName }}</span>
                </div>
                <div class="team">
                  <img :src="item.homeLogo" :alt="item.homeName" class="team-logo">
                  <span class="team-name">{{ item.homeName }}</span>
                </div>
              </div>
              <div class="commentators">
                <div class="commentator" v-for="commentator in item.collectMatchDetailResList" :key="commentator.id"
                  @click="handleToRoom(item.matchId, commentator.userId, item.liveType)">
                  <img :src="commentator.userImage" :alt="commentator.userName" class="commentator-avatar">
                  <div class="tip" v-if="commentator.liveState === 1">LIVE</div>
                  <span class="commentator-name">{{ commentator.userName }}</span>
                </div>
              </div>
              <div class="status-section">
                <div class="status">
                  <img src="@/static/schedule/live-icon-active.png" class="play-icon"
                    @click="handleCancelReserve(item.matchId, item.liveType)">
                  <span class="status-text">{{ formatLiveState(item.liveState) }}</span>
                </div>
              </div>
              <div class="red-line"></div>
            </div>
          </div>
        </div>
      </template>
      <div v-else class="none-box">
        <none-box text="暂无预约" :imageSrc="noReserveImage" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import dayjs from 'dayjs'
import { useRouter } from '@/composables/useRouter'
import { useMessage } from 'naive-ui'
import noReserveImage from '@/static/user/yy-default-icon.png'
import { getCollectMatchPageApi } from '@/api/user'
import { cancelCollectMatchApi } from '@/api/schedule'

const { goToRoom } = useRouter()
const message = useMessage()

// 分页相关
const pagination = reactive({
  current: 1,
  size: 20,
  total: 0,
  loading: false,
  finished: false
})

// 按日期分组预约数据
const groupedAppointments = computed(() => {
  if (appointmentList.value.length === 0) return []
  // 定义分组类型
  type AppointmentGroup = {
    date: string
    displayDate: string
    isToday: boolean
    items: any[]
  }
  const groups: AppointmentGroup[] = []
  // 先按比赛时间排序
  const sortedList = appointmentList.value.sort((a, b) =>
    dayjs(a.matchTime).valueOf() - dayjs(b.matchTime).valueOf()
  )
  // 按日期分组
  sortedList.forEach(item => {
    const matchTime = dayjs(item.matchTime)
    const dateStr = matchTime.format('YYYY-MM-DD')
    const displayDate = matchTime.format('MM月DD日')
    const isToday = matchTime.isSame(dayjs(), 'day')
    const existingGroup = groups.find(g => g.date === dateStr)
    if (existingGroup) {
      existingGroup.items.push(item)
    } else {
      groups.push({
        date: dateStr,
        displayDate,
        isToday,
        items: [item]
      })
    }
  })
  return groups
})

const appointmentList = ref<any[]>([])
// 获取预约比赛列表
const getReserveMatchList = async (loadMore = false) => {
  if (pagination.loading) return
  try {
    pagination.loading = true
    if (!loadMore) {
      // 如果是刷新或首次加载，重置状态
      pagination.current = 1
      pagination.finished = false
      appointmentList.value = []
    }
    const { data } = await getCollectMatchPageApi({
      current: pagination.current,
      size: pagination.size
    })
    if (loadMore) {
      appointmentList.value = [...appointmentList.value, ...data.records]
    } else {
      appointmentList.value = data.records
    }
    pagination.total = data.total
    pagination.finished = data.records.length < pagination.size
    if (!pagination.finished) {
      pagination.current++
    }
  } finally {
    pagination.loading = false
  }
}

const formatLiveState = (value: number) => {
  switch (value) {
    case 0:
      return '未开始'
    case 1:
      return '直播中'
    default:
      break
  }
}

const handleScroll = (e: Event) => {
  const target = e.target as HTMLElement
  // 距离底部100px时加载更多
  const threshold = 100
  const { scrollHeight, scrollTop, clientHeight } = target
  if (scrollHeight - (scrollTop + clientHeight) < threshold && !pagination.loading && !pagination.finished) {
    getReserveMatchList(true)
  }
}

// 跳转直播间
const handleToRoom = async (matchId: number, userId: number, liveTypeEnum: string) => {
  await goToRoom(matchId, userId, liveTypeEnum)
}

// 预约、取消预约比赛
const handleCancelReserve = async (matchId: number, liveType: string) => {
  await cancelCollectMatchApi({ matchId, liveType })
  // 显示消息前先清除所有
  message.destroyAll()
  message.success('取消预约成功')
  await getReserveMatchList()
}

onMounted(() => {
  getReserveMatchList()
})
</script>

<style lang="scss" scoped>
.user-appointments {
  background-color: #fff;
  min-height: 100%;

  .page-header {
    padding: 32px 0 40px 36px;
    display: flex;
    align-items: center;

    .line {
      width: 3px;
      height: 16px;
      margin-right: 8px;
      background: #FB2B1F;
    }

    h3 {
      font-size: 18px;
      font-weight: 600;
      color: #333;
      margin: 0;
    }
  }

  .page-content {
    padding: 0 36px 40px;
    // overflow-y: auto;
    // height: calc(100vh - 400px);

    .date-group {
      margin-bottom: 16px;

      .date-header {
        position: sticky;
        top: 0;
        z-index: 999;
        background: white;
        padding: 8px 0;
        margin-bottom: 16px;

        .date {
          font-size: 16px;
          font-weight: 500;
          color: #333;
          margin-right: 8px;
        }

        .today {
          font-size: 16px;
          font-weight: 400;
          color: #666;
        }
      }
    }

    .appointment-list {
      .appointment-item {
        position: relative;
        display: flex;
        align-items: center;
        padding: 20px 24px;
        margin-bottom: 16px;
        // background: #FAFAFA;
        border: 1px solid #F0F0F0;
        overflow: hidden;
        border-radius: 8px;

        .time-info {
          width: 80px;
          margin-right: 32px;

          .league {
            font-size: 12px;
            color: #999;
            margin-bottom: 4px;
          }

          .time {
            font-size: 16px;
            font-weight: 600;
            color: #333;
          }
        }

        .match-info {
          display: flex;
          min-width: 150px;
          flex-direction: column;
          margin-right: 40px;
          gap: 10px;

          .team {
            display: flex;
            align-items: center;

            .team-logo {
              width: 24px;
              height: 24px;
              margin-right: 8px;
              border-radius: 50%;
            }

            .team-name {
              font-size: 14px;
              color: #333;
              white-space: nowrap;
            }
          }

        }

        .commentators {
          display: flex;
          align-items: center;
          flex: 1;
          gap: 12px;

          .commentator {
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
            cursor: pointer;
            transition: transform ease-in 0.3s;

            &:hover {
              transform: translateY(-4px);
            }

            .tip {
              position: absolute;
              bottom: 22px;
              right: 50%;
              transform: translateX(50%);
              background: #FB2B1F;
              color: #fff;
              font-size: 10px;
              padding: 2px 6px;
              line-height: 1;
              border-radius: 2px;
            }

            .commentator-avatar {
              width: 46px;
              height: 46px;
              border-radius: 50%;
              margin-right: 4px;
            }

            .commentator-name {
              color: #333;
              font-size: 14px;
              border-radius: 2px;
              white-space: nowrap;
              margin-top: 6px;
              max-width: 100px;
              text-overflow: ellipsis;
              overflow: hidden;
            }
          }
        }

        .status-section {
          margin-left: auto;
          margin-right: 16px;

          .status {
            display: flex;
            align-items: center;

            .play-icon {
              width: 20px;
              height: 20px;
              margin-right: 4px;
              color: #333;
              cursor: pointer;
            }

            .status-text {
              font-size: 14px;
              color: #333;
            }
          }
        }

        .red-line {
          position: absolute;
          right: 0;
          top: 0;
          bottom: 0;
          width: 3px;
          background: #FB2B1F;
          border-radius: 0 8px 8px 0;
        }
      }
    }
  }
}
</style>