<template>
  <n-modal v-model:show="visible" :mask-closable="false" preset="card" style="width: 450px;" @close="closeModal">
    <template #header>
      <div class="modal-header">
        <span class="modal-title">登录</span>
      </div>
    </template>
    <n-form class="login-form" ref="formRef" :model="formValue" :show-label="false" :rules="rules">
      <!-- 手机号输入框 -->
      <n-form-item class="form-item" path="phoneNumber">
        <n-input v-model:value="formValue.phoneNumber" placeholder="请输入手机号码" size="large" clearable />
      </n-form-item>
      <!-- 密码输入框 -->
      <n-form-item class="form-item" path="password">
        <n-input v-model:value="formValue.password" type="password" placeholder="请输入6-16位登录密码" size="large"
          show-password-on="click" clearable />
      </n-form-item>
      <!-- 服务协议 -->
      <n-form-item class="agreement" path="agreeTerms">
        <div class="agreement-container">
          <n-checkbox v-model:checked="formValue.agreeTerms" size="small">
            登录即同意本平台的
            <a href="#" class="agreement-link">《用户服务协议》</a>
          </n-checkbox>
          <span class="forget-password" @click="handleToForget">忘记密码</span>
        </div>
      </n-form-item>
      <!-- 登录按钮 -->
      <n-form-item>
        <n-button type="primary" size="large" block :loading="loading" @click="handleLogin" class="login-btn">
          登录
        </n-button>
      </n-form-item>
      <!-- 注册链接 -->
      <div class="register-link">
        <a href="#" @click="switchToRegister">注册新账号</a>
      </div>
    </n-form>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useMessage, type FormInst, type FormItemRule } from 'naive-ui'
import { loginApi } from '@/api/user'
import { useUserStore } from '@/stores/user'
import { useTaskStore } from '@/stores/task'
import { useRouter } from '@/composables/useRouter'
import { validateAgreement, validatePassword, validatePhone } from '@/utils/validate'

interface Props {
  show: boolean
}

interface Emits {
  (e: 'update:show', value: boolean): void
  (e: 'switch-to-register'): void
}
const taskStore = useTaskStore()
const userStore = useUserStore()
const props = defineProps<Props>()
const emit = defineEmits<Emits>()
const message = useMessage()
const { push } = useRouter()
const formRef = ref<FormInst | null>(null)

// 响应式数据
const formValue = ref({
  phoneNumber: '',
  password: '',
  agreeTerms: true
})

const loading = ref(false)

// 表单校验规则
const rules = {
  phoneNumber: [
    { required: true, validator: validatePhone, trigger: ['blur', 'input'] }
  ],
  password: [
    { required: true, validator: validatePassword, trigger: ['blur', 'input'] }
  ],
  agreeTerms: [
    { required: true, validator: validateAgreement, trigger: ['change'] }
  ]
}

// 计算属性
const visible = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

// 方法
const closeModal = () => {
  formValue.value.phoneNumber = ''
  formValue.value.password = ''
  formValue.value.agreeTerms = false
  visible.value = false
}

// 登录
const handleLogin = (e: MouseEvent) => {
  e.preventDefault()
  formRef.value?.validate(async (errors) => {
    if (!errors) {
      loading.value = true
      try {
        const { data } = await loginApi({
          phone: formValue.value.phoneNumber,
          password: formValue.value.password
        })
        console.log('登录成功', data)
        userStore.setUserData(data.token, data.userDetail)
        message.success('登录成功')
        taskStore.getConfigTodoList()
        closeModal()
      } catch (error) {

      } finally {
        loading.value = false
      }
    }
  })
}

const handleToForget = async () => {
  visible.value = false;
  await push('/global-modify-password')
}

const switchToRegister = () => {
  emit('switch-to-register')
}
</script>

<style lang="scss" scoped>
.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 30px 40px 0 40px;

  .modal-title {
    font-size: 24px;
    font-weight: 600;
    color: #333333;
  }

  .close-icon {
    font-size: 20px;
    color: #999999;
    cursor: pointer;

    &:hover {
      color: #333333;
    }
  }
}

.login-form {
  padding: 20px 20px;

  .form-item {
    margin-bottom: 6px;
  }

  .agreement :deep(.n-form-item-feedback-wrapper) {
    margin-top: -10px;
  }

  .agreement {

    .agreement-container {
      display: flex;
      justify-content: space-between;
      width: 100%;
      align-items: center;
    }

    .forget-password {
      color: #818181;
      text-decoration: none;
      cursor: pointer;

      &:hover {
        text-decoration: underline;
      }
    }

    .agreement-link {
      color: #FB2B1F;
      text-decoration: none;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  .login-btn {
    margin-top: 10px;
    background: #FB2B1F;
    border: none;
    border-radius: 24px;
    height: 48px;
    font-size: 16px;
    font-weight: 600;

    &:hover {
      background: #e02419;
    }
  }

  .register-link {
    text-align: center;

    a {
      color: #666666;
      text-decoration: none;
      font-size: 14px;

      &:hover {
        color: #FB2B1F;
        text-decoration: underline;
      }
    }
  }
}
</style>
