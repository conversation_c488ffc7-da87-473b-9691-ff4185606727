import router from '../router/index'

// 添加路由加载状态管理
let isNavigating = false

router.beforeEach(async (to, from, next) => {
  try {
    // 防止重复导航
    if (isNavigating) {
      console.warn('路由正在跳转中，忽略重复请求')
      return
    }

    isNavigating = true

    // 设置页面标题
    if (to.meta?.title) {
      window.document.title = to.meta.title as string
    } else {
      window.document.title = '有料比分'
    }

    // 验证路由是否存在
    if (to.matched.length === 0) {
      console.error('路由不存在:', to.path)
      next('/404')
      return
    }

    console.log('路由跳转:', from.path, '->', to.path)
    next()
  } catch (error) {
    console.error('路由守卫错误:', error)
    next(false) // 取消导航
  }
})

router.afterEach((to, _from, failure) => {
  isNavigating = false

  if (failure) {
    console.error('路由跳转失败:', failure)
  } else {
    console.log('路由跳转成功:', to.path)
  }
})

// 处理路由错误
router.onError((error) => {
  isNavigating = false
  console.error('路由错误:', error)

  // 如果是组件加载失败，尝试重新加载
  if (error.message.includes('Loading chunk')) {
    console.log('检测到组件加载失败，尝试刷新页面')
    window.location.reload()
  }
})

export default router
